import React from 'react';

const FinancialEvolution: React.FC = () => {
  return (
    <article className="card card-financial-evolution">
      <h2 className="card-title">Evolução do Perfil Financeiro</h2>
      <div className="line-chart-container">
        <svg width="100%" height="100%" viewBox="0 0 500 150" preserveAspectRatio="none">
          {/* Grid lines */}
          <line x1="0" y1="30" x2="500" y2="30" stroke="#333344" strokeWidth="1"/>
          <line x1="0" y1="75" x2="500" y2="75" stroke="#333344" strokeWidth="1"/>
          <line x1="0" y1="120" x2="500" y2="120" stroke="#333344" strokeWidth="1"/>

          {/* Data Lines */}
          <polyline fill="none" stroke="#ff3b30" strokeWidth="2" points="10,110 170,112 330,115 490,118" />
          <polyline fill="none" stroke="#af52de" strokeWidth="2" points="10,90 170,95 330,98 490,95" />
          <polyline fill="none" stroke="#007aff" strokeWidth="2" points="10,50 170,52 330,48 490,45" />
          <polyline fill="none" stroke="#34c759" strokeWidth="2" points="10,70 170,68 330,72 490,75" />
          
          {/* Data Points */}
          <circle cx="10" cy="110" r="3" fill="#ff3b30"/>
          <circle cx="170" cy="112" r="3" fill="#ff3b30"/>
          <circle cx="330" cy="115" r="3" fill="#ff3b30"/>
          <circle cx="490" cy="118" r="3" fill="#ff3b30"/>
          
          <circle cx="10" cy="90" r="3" fill="#af52de"/>
          <circle cx="170" cy="95" r="3" fill="#af52de"/>
          <circle cx="330" cy="98" r="3" fill="#af52de"/>
          <circle cx="490" cy="95" r="3" fill="#af52de"/>
          
          <circle cx="10" cy="50" r="3" fill="#007aff"/>
          <circle cx="170" cy="52" r="3" fill="#007aff"/>
          <circle cx="330" cy="48" r="3" fill="#007aff"/>
          <circle cx="490" cy="45" r="3" fill="#007aff"/>
          
          <circle cx="10" cy="70" r="3" fill="#34c759"/>
          <circle cx="170" cy="68" r="3" fill="#34c759"/>
          <circle cx="330" cy="72" r="3" fill="#34c759"/>
          <circle cx="490" cy="75" r="3" fill="#34c759"/>
        </svg>
      </div>
      <div className="line-chart-legend">
        <div className="legend-item">
          <div className="legend-color-box" style={{ backgroundColor: '#ff3b30' }} />
          Superendividados (-15%)
        </div>
        <div className="legend-item">
          <div className="legend-color-box" style={{ backgroundColor: '#af52de' }} />
          Endividados (-10%)
        </div>
        <div className="legend-item">
          <div className="legend-color-box" style={{ backgroundColor: '#007aff' }} />
          Equilibrados (+12%)
        </div>
        <div className="legend-item">
          <div className="legend-color-box" style={{ backgroundColor: '#34c759' }} />
          Investidores (+25%)
        </div>
      </div>
    </article>
  );
};

export default FinancialEvolution;