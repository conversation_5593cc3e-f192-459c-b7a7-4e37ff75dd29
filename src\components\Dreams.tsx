import React from 'react';
import { DreamsData } from '../types/dashboard';

interface DreamsProps {
  data: DreamsData;
}

const Dreams: React.FC<DreamsProps> = ({ data }) => {
  const circumference = 2 * Math.PI * 15.9155;
  const strokeDasharray = `${(data.percentage / 100) * circumference}, ${circumference}`;

  return (
    <article className="card card-dreams">
      <h2 className="card-title">Sonhos: Cadastrados vs Realizados</h2>
      <div className="dreams-content">
        <div className="radial-chart-container">
          <svg width="150" height="150" viewBox="0 0 36 36">
            <path 
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
              fill="none" 
              stroke="#333344" 
              strokeWidth="3" 
            />
            <path 
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
              fill="none" 
              stroke="#34c759" 
              strokeWidth="3" 
              strokeDasharray={strokeDasharray}
              strokeLinecap="round" 
            />
          </svg>
          <div className="radial-chart-text">
            <div className="percentage">{data.percentage}%</div>
            <div className="label">Progresso</div>
          </div>
        </div>
        <div className="dreams-stats">
          <div>
            <div className="value">{data.registered.toLocaleString()}</div>
            <div className="label">Cadastrados</div>
          </div>
          <div>
            <div className="value">{data.achieved.toLocaleString()}</div>
            <div className="label">Realizados</div>
          </div>
        </div>
      </div>
    </article>
  );
};

export default Dreams;