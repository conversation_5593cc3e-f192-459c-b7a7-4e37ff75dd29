/* --- CSS VARIABLES & THEME --- */
:root {
  --color-bg-primary: #12121c;
  --color-bg-secondary: #1c1c28;
  --color-border: #333344;
  --color-text-primary: #f0f0f5;
  --color-text-secondary: #a0a0b0;
  --color-text-tertiary: #6c6c80;

  --color-accent: #007aff;
  --color-green: #34c759;
  --color-teal: #5ac8fa;
  --color-purple: #af52de;
  --color-orange: #ff9500;
  --color-red: #ff3b30;

  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --border-radius: 12px;
}

/* --- GLOBAL STYLES --- */
body {
  font-family: var(--font-family-sans);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  margin: 0;
  padding: 2rem;
}

/* --- DASHBOARD LAYOUT --- */
.dashboard {
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
}

.logout-button {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  z-index: 10;
}

.logout-button:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.dashboard-header h1 span {
  font-weight: 400;
  font-size: 1rem;
  color: var(--color-text-secondary);
  display: block;
  margin-top: 4px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

/* --- CARD STYLES --- */
.card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header .card-title {
  margin-bottom: 0;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--color-text-primary);
  margin-top: 0;
}

/* --- GRID PLACEMENT --- */
.card-top-employees { grid-column: span 2; }
.card-journey-progress { grid-column: span 2; }
.card-dreams { grid-column: span 1; }
.card-financial-profile { grid-column: span 1; }
.card-age-range { grid-column: span 1; }
.card-financial-situation { grid-column: span 1; }
.card-financial-evolution { grid-column: span 4; }
.card-interests { grid-column: span 2; }
.card-financial-objectives { grid-column: span 2; }
.card-insights { grid-column: span 4; }

/* --- LOADING & ERROR STATES --- */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--color-text-secondary);
  font-style: italic;
}

.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--color-red);
  text-align: center;
}

/* --- EMPLOYEE LIST --- */
.employee-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.employee-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--color-purple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.employee-name {
  font-weight: 500;
  margin: 0;
}

.employee-email {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.employee-score {
  font-weight: 600;
  color: var(--color-accent);
}

.employee-conquests {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.employee-stats {
  text-align: right;
}

.ver-mais-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-family: var(--font-family-sans);
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

.ver-mais-button:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

/* --- MODAL STYLES --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.modal-close-button {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-employee-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-employee-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modal-employee-item:hover {
  background-color: rgba(0, 122, 255, 0.05);
  border-color: var(--color-accent);
}

.employee-rank {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-accent);
  min-width: 2rem;
  text-align: center;
}

/* --- PROGRESS BARS --- */
.progress-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.progress-bar {
  height: 8px;
  width: 100%;
  background-color: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease-out;
}

/* --- DREAMS RADIAL CHART --- */
.dreams-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.radial-chart-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin-bottom: 1rem;
}

.radial-chart-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radial-chart-text .percentage {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

.radial-chart-text .label {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.dreams-stats {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  text-align: center;
}

.dreams-stats > div {
  padding: 0.5rem 1rem;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 8px;
}

.dreams-stats .value {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.dreams-stats .label {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin: 0;
}

/* --- BAR CHARTS --- */
.bar-chart {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 150px;
  gap: 1rem;
}

.bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  gap: 0.5rem;
}

.bar {
  width: 60%;
  background-color: var(--color-accent);
  opacity: 0.6;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease-out, opacity 0.3s;
}

.bar:hover {
  opacity: 1;
}

.bar-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  text-align: center;
}

/* --- HORIZONTAL BARS --- */
.horizontal-bar-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.horizontal-bar-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.horizontal-bar-label-container {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

.horizontal-progress-bar {
  height: 12px;
  width: 100%;
  background-color: var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.horizontal-progress-bar-fill {
  height: 100%;
  border-radius: 6px;
  background-color: var(--color-orange);
  transition: width 0.3s ease-out;
}

/* --- LINE CHART --- */
.line-chart-container {
  position: relative;
  width: 100%;
  height: 250px;
}

.line-chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.legend-color-box {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

/* --- TREEMAP --- */
.treemap {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr;
  grid-template-rows: 1.5fr 1fr 1.2fr;
  gap: 10px;
  height: 100%;
  min-height: 200px;
}

.treemap-item {
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 500;
}

.item-familia { grid-column: 1 / 2; grid-row: 1 / 3; background-color: rgba(90, 200, 250, 0.4); }
.item-viagens { grid-column: 2 / 4; grid-row: 1 / 2; background-color: rgba(255, 59, 48, 0.3); }
.item-filmes { grid-column: 2 / 3; grid-row: 2 / 3; background-color: rgba(175, 82, 222, 0.4); }
.item-pets { grid-column: 1 / 2; grid-row: 3 / 4; background-color: rgba(175, 82, 222, 0.3); }
.item-esportes { grid-column: 2 / 3; grid-row: 3 / 4; background-color: rgba(52, 199, 89, 0.3); }
.item-culinaria { grid-column: 3 / 4; grid-row: 3 / 4; background-color: rgba(255, 149, 0, 0.3); }
.item-saude { grid-column: 3 / 4; grid-row: 2 / 3; background-color: rgba(52, 199, 89, 0.4); }

/* --- INSIGHTS --- */
.card.card-insights {
  background-color: transparent;
  border: none;
  padding: 0;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
}

.insight-item {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.insight-icon {
  background-color: var(--color-border);
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-value {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.insight-label {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .card-financial-evolution, .card-insights { grid-column: span 2; }
  .card-interests, .card-financial-objectives { grid-column: span 2; }
}

@media (max-width: 768px) {
  body { padding: 1rem; }
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  .card-top-employees, .card-journey-progress, .card-dreams,
  .card-financial-profile, .card-age-range, .card-financial-situation,
  .card-financial-evolution, .card-interests, .card-financial-objectives,
  .card-insights {
    grid-column: span 1;
  }
  .insights-grid {
    grid-template-columns: 1fr;
  }
  .line-chart-legend {
    flex-direction: column;
    gap: 1rem;
  }
}
