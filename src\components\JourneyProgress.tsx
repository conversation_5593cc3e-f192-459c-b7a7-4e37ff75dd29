import React from 'react';
import { ProgressItem } from '../types/dashboard';

interface JourneyProgressProps {
  progressData: ProgressItem[];
}

const JourneyProgress: React.FC<JourneyProgressProps> = ({ progressData }) => {
  return (
    <article className="card card-journey-progress">
      <h2 className="card-title">Progresso na Jornada</h2>
      <div className="progress-list">
        {progressData.map((item, index) => (
          <div key={index} className="progress-item">
            <div className="progress-label">
              <span>{item.label}</span>
              <span>{item.percentage}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-bar-fill" 
                style={{ 
                  width: `${item.percentage}%`, 
                  backgroundColor: item.color 
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </article>
  );
};

export default JourneyProgress;