import React from 'react';
import { Wallet, Mail, TrendingUp, XCircle } from 'lucide-react';
import { InsightData } from '../types/dashboard';

interface AdditionalInsightsProps {
  insights: InsightData[];
}

const AdditionalInsights: React.FC<AdditionalInsightsProps> = ({ insights }) => {
  const getIcon = (iconName: string) => {
    const iconProps = { size: 24 };
    switch (iconName) {
      case 'wallet': return <Wallet {...iconProps} />;
      case 'mail': return <Mail {...iconProps} />;
      case 'trending-up': return <TrendingUp {...iconProps} />;
      case 'x-circle': return <XCircle {...iconProps} />;
      default: return <Wallet {...iconProps} />;
    }
  };

  return (
    <article className="card card-insights">
      <div className="insights-grid">
        {insights.map((insight, index) => (
          <div key={index} className="insight-item">
            <div className="insight-icon" style={{ color: insight.color }}>
              {getIcon(insight.icon)}
            </div>
            <div>
              <div className="insight-value">{insight.value}</div>
              <div className="insight-label">{insight.label}</div>
            </div>
          </div>
        ))}
      </div>
    </article>
  );
};

export default AdditionalInsights;