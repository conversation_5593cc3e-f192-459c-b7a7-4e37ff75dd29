import React from 'react';
// 1. Restore your original, correct import
import { BarChartData } from '../types/dashboard';
import '../styles/barchart.css';

interface BarChartProps {
  data: BarChartData[];
  title: string;
  className?: string;
}

const BarChart: React.FC<BarChartProps> = ({ data, title, className = '' }) => {
  const numericValues = data.map(item => Number(item.value)).filter(v => !isNaN(v));
  const maxValue = numericValues.length > 0 ? Math.max(...numericValues) : 0;
  const hasData = data.length > 0 && maxValue > 0;

  return (
    <article className={`card ${className}`}>
      <h2 className="card-title">{title}</h2>
      <div className="bar-chart-container">
        {hasData ? (
          data.map((item, index) => {
            const itemValue = Number(item.value);
            const barHeight = maxValue > 0 ? (itemValue / maxValue) * 100 : 0;

            return (
              <div key={index} className="bar-wrapper">
                <div className="bar-value">{item.value}</div>
                <div
                  className="bar"
                  style={{
                    // 2. Use the type-safe backgroundColor property
                    backgroundColor: item.color || '#007aff',
                    height: `${Math.max(barHeight, 2)}%`,
                  }}
                />
                <div className="bar-label">{item.label}</div>
              </div>
            );
          })
        ) : (
          <div className="no-data">Nenhum dado disponível</div>
        )}
      </div>
    </article>
  );
};

export default BarChart;