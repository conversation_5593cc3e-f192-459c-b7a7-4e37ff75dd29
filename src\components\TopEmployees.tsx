import React from 'react';
import { useState } from 'react';
import { Eye } from 'lucide-react';
import { Employee } from '../types/dashboard';
import EmployeesModal from './EmployeesModal';

interface TopEmployeesProps {
  employees: Employee[];
  allEmployees?: Employee[];
}

const TopEmployees: React.FC<TopEmployeesProps> = ({ employees, allEmployees = [] }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const hasMoreEmployees = allEmployees.length > employees.length;

  return (
    <>
      <article className="card card-top-employees">
        <div className="card-header">
          <h2 className="card-title">Top Colaboradores Engajados</h2>
          {hasMoreEmployees && (
            <button 
              className="ver-mais-button"
              onClick={() => setIsModalOpen(true)}
            >
              Ver Mais <Eye size={16} />
            </button>
          )}
        </div>
        <ul className="employee-list">
          {employees.map((employee) => (
            <li key={employee.id} className="employee-item">
              <div className="employee-info">
                <div 
                  className="employee-avatar" 
                  style={{ backgroundColor: employee.avatarColor }}
                >
                  {employee.initials}
                </div>
                <div>
                  <div className="employee-name">{employee.name}</div>
                  <div className="employee-email">{employee.email}</div>
                </div>
              </div>
              <div className="employee-stats">
                <div className="employee-score">{employee.dcoins.toLocaleString()}</div>
                <div className="employee-conquests">{employee.achievements} Conquistas</div>
              </div>
            </li>
          ))}
        </ul>
      </article>
      
      <EmployeesModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        employees={allEmployees}
      />
    </>
  );
};

export default TopEmployees;